using Amazon.BedrockAgent;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.Runtime.Internal.Transform;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using platform.Constants;
using platform.Models.Configuration;
using platform.Models.Document;
using platform.Models.Document.AWS;
using platform.Models.Response;
using platform.Services;
using shared.Components.ApiEventBus;
using shared.Controllers;
using shared.Extension;
using shared.Models.Response;

namespace platform.Controllers
{
    [Route(Routes.AgentAliasController.BasePath)]
    [Produces("application/json")]
    public class AgentAliasController : SuperController
    {
        private readonly ILogger<AgentController> logger;

        public AgentAliasController(ILogger<AgentController> logger, IApiEventBus apiEventBus, IDynamoDBContext dynamoDBContext, IAmazonBedrockAgent bedrockAgent, IIAM iAM, IOptionsMonitor<BedrockConfiguration> bedrockConfiguration) : base(apiEventBus, dynamoDBContext)
        {
            this.logger = logger;
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentAliasController.Public.LIST)]
        public async Task<IActionResult> ListAliases([FromQuery] int count = 0, [FromQuery] string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;

            var accountId = GetAccountId();
            var listEntriesInternal = await GetDBEntries<AgentAlias>(nameof(AgentAlias.AccountId), accountId, count, nextToken, AgentAlias.AccountIdAliasIndex, getTotal: nextToken == null);

            var agent_ids = listEntriesInternal.Entries.Select((value, index) => new { indexName=$":id{index}", value.AgentId }).ToList();

            var agents = await BatchGetDBEntry<AwsAgent>(accountId, agent_ids.Select(e => e.AgentId).ToList());

            var agent_names = agents.ToDictionary(value => value.AgentId, value => value.Name);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AgentAlias, AgentAliasResponse>();
            }, loggerFactory).CreateMapper();

            var result = new ListResponse<AgentAliasResponse>()
            {
                Entries = mapper.Map<IList<AgentAlias>, IList<AgentAliasResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            result.Entries.ForEach(entry =>
            {
                entry.AgentName = agent_names.GetOrDefault(entry.AgentId, "#MISSING");
            });

            return Ok(result);
        }
    }
}