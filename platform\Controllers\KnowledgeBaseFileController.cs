﻿using Microsoft.AspNetCore.Mvc;
using platform.Constants;
using Microsoft.AspNetCore.Authorization;
using shared.Controllers;
using Amazon.DynamoDBv2.DataModel;
using Amazon.S3;
using shared.Components.ApiEventBus;
using platform.Models.Document;
using platform.Models.Enum;
using platform.Components.AWS;
using Microsoft.Extensions.Options;
using platform.Models.Configuration;
using Amazon.IdentityManagement.Model;
using Amazon.BedrockAgent.Model;
using shared.Models.Response;
using platform.Models.Response;
using AutoMapper;
using platform.Models.Document.AWS;
using System.Globalization;
using Amazon.S3.Model;
using platform.Models.Request;

namespace platform.Controllers
{
    [Route(Routes.KnowledgeBaseFileController.BasePath)]
    [Produces("application/json")]
    public class KnowledgeBaseFileController : SuperController
    {

        private readonly ILogger<KnowledgeBaseFileController> logger;
        private readonly IAmazonS3 fileContext;
        private readonly IOptionsMonitor<BedrockConfiguration> bedrockConfiguration;

        public KnowledgeBaseFileController(ILogger<KnowledgeBaseFileController> logger, IApiEventBus apiEventBus, IAmazonS3 fileContext,IDynamoDBContext dynamoDBContext, IOptionsMonitor<BedrockConfiguration> bedrockConfiguration) : base(apiEventBus, dynamoDBContext)
        {
            this.fileContext = fileContext;
            this.logger = logger;
            this.bedrockConfiguration = bedrockConfiguration;
        }

        private string GetUploadPath(string accountId, string kbId, string fileId)
        {
            return $"kbs/AccountId={accountId}/KbId={kbId}/{fileId}";
        }

        /*
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Routes.KnowledgeBaseFileController.Public.UPLOAD)]
        public async Task<IActionResult> UploadSinglePart(string kbId)
        {

            string accountId = GetAccountId();

            var files = HttpContext.Request.Form.Files;
            if (files == null || files.Count == 0)
            {
                return BadRequest("No files supplied");
            }

            if (files.Count > 10)
            {
                return BadRequest("You can only upload up to 10 files at a time.");
            }

            var totalSize = files.Sum(m => m.Length);

            if (totalSize > 20 * 1024 * 1024)
            {
                return BadRequest("Total file size is higher than 20Mb, to upload large files, use the multipart upload.");
            }

            foreach (var file in files)
            {
                if (file.Length > 20 * 1024 * 1024) return BadRequest("File size over the limit");
            }

            Models.Document.KnowledgeBase knowledgeBase = await GetDBEntry<Models.Document.KnowledgeBase>(accountId, kbId);
            if (knowledgeBase.Status != KnowledgeBaseStatus.READY)
            {
                return BadRequest("Knowledgebase ust be in READY state to allow data uploading.");
            }

            var bw = dBContext.CreateBatchWrite<KnowledgeBaseFile>();
            List<(IFormFile, KnowledgeBaseFile)> kbFiles = new List<(IFormFile, KnowledgeBaseFile)>();
            foreach (var file in files)
            {
                KnowledgeBaseFile kbFile = new KnowledgeBaseFile();
                kbFile.FileId = Guid.NewGuid().ToString();
                kbFile.KbId = kbId;
                kbFile.AccountId = accountId;
                kbFile.FileName = file.FileName;
                kbFile.UploadedFileSize = 0;
                kbFile.Status = KnowledgeBaseFileUploadStatus.QUEUED;
                kbFile.IsDeployed = false;

                kbFiles.Add((file, kbFile));
                bw.AddPutItem(kbFile);
            }
            await bw.ExecuteAsync();

            List<KnowledgeBaseFile> uploads = new List<KnowledgeBaseFile>();
            List<KnowledgeBaseFile> failed = new List<KnowledgeBaseFile>();


            var bucketName = bedrockConfiguration.CurrentValue.KnowledgeBaseBucketArn.Split(':').Last();
            foreach (var fileTuple in kbFiles)
            {
                if (await S3Helper.UploadFullFile(fileContext, fileTuple.Item1, bucketName, GetUploadPath(accountId, kbId, fileTuple.Item2.FileId)))
                {
                    fileTuple.Item2.Status = KnowledgeBaseFileUploadStatus.READY;
                    fileTuple.Item2.UploadedFileSize = fileTuple.Item1.Length;
                    uploads.Add(fileTuple.Item2);
                }
                else
                {
                    fileTuple.Item2.Status = KnowledgeBaseFileUploadStatus.FAILED;
                    failed.Add(fileTuple.Item2);
                }
            }

            bw = dBContext.CreateBatchWrite<KnowledgeBaseFile>();
            bw.AddPutItems(uploads);
            await bw.ExecuteAsync();

            return Ok(failed);
        }*/


        private async Task<List<KnowledgeBaseFile>> GetPendingFiles(Models.Document.KnowledgeBase knowledgeBase)
        {
            var config = new DynamoDBOperationConfig();
            config.QueryFilter = new List<ScanCondition>() { new ScanCondition("Status", Amazon.DynamoDBv2.DocumentModel.ScanOperator.NotEqual, new object[] { KnowledgeBaseFileUploadStatus.READY }) };
            var search = dBContext.QueryAsync<KnowledgeBaseFile>(knowledgeBase.KbId, config);
            return await search.GetNextSetAsync();
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseFileController.Public.LIST_FILES_FOR_KNOWLEDGEBASE)]
        public async Task<IActionResult> GetFilesForKnowledgebase([FromQuery] string kbId, [FromQuery] string? previousToken = null)
        {
            string accountId = GetAccountId();
            ListResponse<KnowledgeBaseFile> listResponse = await GetDBEntries<KnowledgeBaseFile>(nameof(KnowledgeBaseFile.KbId), kbId, 1000, previousToken);
            listResponse.Entries = listResponse.Entries.Where((item) => item.AccountId == accountId).ToList();

            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<KnowledgeBaseFile, KnowledgeBaseFileResponse>();
            }).CreateMapper();

            var resp = new ListResponse<KnowledgeBaseFileResponse>()
            {
                NextToken = listResponse.NextToken,
                Total = listResponse.Total,
                Entries = mapper.Map<IList<KnowledgeBaseFile>, IList<KnowledgeBaseFileResponse>>(listResponse.Entries).ToList(),
            };

            return Ok(resp);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseFileController.Public.DOWNLOAD)]
        public async Task<IActionResult> DownloadFile(string fileId)
        {
            var accountId = GetAccountId();
            var file = await GetDBEntry<KnowledgeBaseFile>(accountId, fileId, KnowledgeBaseFile.AccountIdFileIdIndexName, nameof(KnowledgeBaseFile.FileId));

            if (file == null) {
                return NotFound();
            }

            string fileKey = GetUploadPath(accountId, file.KbId, file.FileId);
            string bucket = bedrockConfiguration.CurrentValue.KnowledgeBaseBucketArn.Split(':').Last();

            var request = new GetObjectRequest { BucketName = bucket, Key = fileKey };
            var response = await fileContext.GetObjectAsync(request);
            var mimeType = response.Metadata["x-amz-meta-content-type"];
            return new FileStreamResult(response.ResponseStream, mimeType) { FileDownloadName = file.FileName };
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpDelete(Constants.Routes.KnowledgeBaseFileController.Public.DELETE_MULTIPLE)]
        public async Task<IActionResult> DeleteMultiple([FromBody] KnowledgeBaseFileDeleteRequest request)
        {
            var accountId = GetAccountId();
            string bucket = bedrockConfiguration.CurrentValue.KnowledgeBaseBucketArn.Split(':').Last();
            var resp = new DeleteMultiResponse();
            var bd = dBContext.CreateBatchWrite<KnowledgeBaseFile>();

            foreach (string fileId in request.Ids)
            {
                var kbfile = await GetDBEntry<KnowledgeBaseFile>(accountId, fileId, KnowledgeBaseFile.AccountIdFileIdIndexName, nameof(KnowledgeBaseFile.FileId));
                if (kbfile == null) { continue; }
                string fileKey = GetUploadPath(accountId, kbfile.KbId, kbfile.FileId);

                if(await S3Helper.DeleteFile(fileContext, bucket, fileKey))
                {
                    bd.AddDeleteItem(kbfile);
                }
                resp.DeletedIds.Add(fileId);
            }
            await bd.ExecuteAsync();

            return Ok(resp);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Routes.KnowledgeBaseFileController.Public.UPLOAD)]
        public async Task<IActionResult> Upload(string kbId)
        {
            long size = Request.Form.Files.Sum(f => f.Length);

            if (size > **********) return StatusCode(413);

            List<KnowledgeBaseFile> uploadedFiles = new List<KnowledgeBaseFile>();
            string accountId = GetAccountId();
            string bucket = bedrockConfiguration.CurrentValue.KnowledgeBaseBucketArn.Split(':').Last();

            foreach (var file in Request.Form.Files)
            {
                KnowledgeBaseFile kbFile = new KnowledgeBaseFile();
                kbFile.KbId = kbId;
                kbFile.UploadedFileSize = file.Length;
                kbFile.FileName = file.FileName;
                kbFile.FileId = Guid.NewGuid().ToString();
                kbFile.AccountId = accountId;
                kbFile.IsDeployed = false;
                kbFile.Status = KnowledgeBaseFileUploadStatus.READY;

                string key = GetUploadPath(accountId, kbFile.KbId, kbFile.FileId);

                if (await S3Helper.UploadFullFile(fileContext, file, bucket, key))
                {
                    uploadedFiles.Add(kbFile);
                }
            }

            try
            {
                var bw = dBContext.CreateBatchWrite<KnowledgeBaseFile>();
                bw.AddPutItems(uploadedFiles);
                await bw.ExecuteAsync();
            }catch(Exception ex)
            {
                return StatusCode(500, ex.Message);
            }

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<KnowledgeBaseFile, KnowledgeBaseFileResponse>();
            }, loggerFactory).CreateMapper();


            var httpResponse = new ListResponse<KnowledgeBaseFileResponse>()
            {
                Entries = mapper.Map<IList<KnowledgeBaseFile>, IList<KnowledgeBaseFileResponse>>(uploadedFiles).ToList(),
            };


            return Ok(httpResponse);

        }


    }
}
