<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>coral_agents</RootNamespace>
    <UserSecretsId>cc29384f-3f23-4d42-9f6b-471875629656</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Extensions.CognitoAuthentication" Version="3.1.0" />
    <PackageReference Include="AWSSDK.BedrockAgent" Version="4.0.4" />
    <PackageReference Include="AWSSDK.BedrockRuntime" Version="4.0.1.3" />
    <PackageReference Include="AWSSDK.Core" Version="4.0.0.16" />
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="4.0.2.3" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.2" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\shared-components\shared.csproj" />
  </ItemGroup>

</Project>
